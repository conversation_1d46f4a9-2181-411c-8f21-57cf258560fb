import React, { useState, useEffect } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { FaShoppingCart, FaTag, FaArrowLeft } from 'react-icons/fa'
import useCartStore from '../../store/cartStore'
import useAuthStore from '../../store/authStore'
import Button from '../../shared/ui/Button'
import CartItem from '../components/CartItem'
import LoginPromptModal from '../../shared/ui/LoginPromptModal'


const Cart = () => {
  const navigate = useNavigate()

  // Cart store
  const cartItems = useCartStore(state => state.cartItems)
  const getSubtotal = useCartStore(state => state.getSubtotal)
  const getTotalQuantity = useCartStore(state => state.getTotalQuantity)


  // Auth store
  const isAuthenticated = useAuthStore(state => state.isAuthenticated)

  const [showLoginPrompt, setShowLoginPrompt] = useState(false)

  // Check authentication on component mount
  useEffect(() => {
    if (!isAuthenticated) {
      setShowLoginPrompt(true)
    }
  }, [isAuthenticated])

  // Format currency consistently
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(amount)
  }

  // Use derived values from the store
  const subtotal = getSubtotal()

  // Empty cart state
  if (cartItems.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50">
        {/* Breadcrumb */}
        <div className="bg-white border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <nav className="flex items-center space-x-2 text-sm">
              <Link to="/" className="text-gray-500 hover:text-primary">Home</Link>
              <span className="text-gray-400">/</span>
              <span className="text-gray-900 font-medium">Shopping Cart</span>
            </nav>
          </div>
        </div>

        {/* Empty Cart Content */}
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <FaShoppingCart className="w-24 h-24 text-gray-300 mx-auto mb-8" />
            <h1 className="text-3xl font-bold text-gray-900 mb-4">Your cart is empty</h1>
            <p className="text-lg text-gray-600 mb-8 max-w-md mx-auto">
              Looks like you haven't added any divine items to your cart yet.
              Start shopping to fill it up!
            </p>

            <div className="space-y-4 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center">
              <Button
                variant="primary"
                onClick={() => navigate('/products')}
                className="w-full sm:w-auto px-8 py-3"
              >
                Continue Shopping
              </Button>
              <Button
                variant="outline"
                onClick={() => navigate('/')}
                className="w-full sm:w-auto px-8 py-3"
              >
                <FaArrowLeft className="w-4 h-4 mr-2" />
                Back to Home
              </Button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  const handleCheckout = () => {
    if (isAuthenticated) {
      navigate('/checkout')
    } else {
      navigate('/login')
    }
  }

  return (
    <div className="min-h-screen ">
      {/* Breadcrumb */}
      <div className="border-b border-gray-300">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <nav className="flex items-center space-x-2 text-sm">
            <Link to="/" className="text-gray-500 hover:text-primary">Home</Link>
            <span className="text-gray-400">/</span>
            <span className="text-gray-900 font-medium">Shopping Cart</span>
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl  mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Cart Items */}
          <div className="lg:col-span-2 space-y-6">
            {/* Cart Items List */}
            <div className="bg-white rounded-lg shadow-sm border">
              {/* Header inside cart container */}
              <div className="p-6 border-b">
                <h1 className="text-3xl font-bold text-gray-900 mb-2">Shopping Cart</h1>
                <p className="text-gray-600">
                  {getTotalQuantity()} {getTotalQuantity() === 1 ? 'item' : 'items'} in your cart
                </p>
              </div>
              {/* Header */}
              {/* Product Header */}
              <div className="hidden md:grid grid-cols-3 gap-4 px-6 py-4 border-b ">
                <div className="col-span-2">
                  <h3 className="font-semibold text-gray-900">Product</h3>
                </div>
                <div className="text-right">
                  <h3 className="font-semibold text-gray-900">Subtotal</h3>
                </div>
              </div>

              {/* Items */}
              <div className="divide-y divide-gray-200 p-4">
                {cartItems.map(item => (
                  <CartItem key={item.id} item={item} />
                ))}
              </div>
            </div>


          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm border p-6 sticky top-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-6">Order Summary</h2>

              <div className="space-y-4">
                {/* Items Count */}
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">
                    Items ({getTotalQuantity()})
                  </span>
                  <span className="font-medium">{formatCurrency(subtotal)}</span>
                </div>

                {/* Shipping */}
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Shipping</span>
                  <span className="font-medium text-gray-600">
                    {subtotal >= 500 ? 'Free' : formatCurrency(50)}
                  </span>
                </div>



                {/* Divider */}
                <div className="border-t border-gray-200 pt-4">
                  <div className="flex justify-between">
                    <span className="text-lg font-semibold text-gray-900">Total</span>
                    <span className="text-lg font-bold text-primary">
                      {formatCurrency(subtotal + (subtotal >= 500 ? 0 : 50))}
                    </span>
                  </div>
                </div>

                {/* Free Shipping Notice */}
                {subtotal < 500 && (
                  <div className="bg-background border border-gray-400 rounded-lg p-3">
                    <p className="text-gray-900 text-sm">
                      Add {formatCurrency(500 - subtotal)} more for free shipping!
                    </p>
                  </div>
                )}
              </div>

              {/* Checkout Button */}
              <Button
                onClick={handleCheckout}
                className="w-full mt-6 py-3"
                variant="primary"
              >
                {isAuthenticated ? 'Proceed to Checkout' : 'Login to Checkout'}
              </Button>

              {/* Continue Shopping */}
              <Button
                onClick={() => navigate('/products')}
                variant="outline"
                className="w-full mt-3 py-3"
              >
                Continue Shopping
              </Button>

              {/* Security Notice */}
              <div className="mt-6 text-center">
                <p className="text-xs text-gray-500">
                  🔒 Secure checkout with SSL encryption
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Login Prompt Modal */}
      <LoginPromptModal
        isOpen={showLoginPrompt}
        onClose={() => {
          setShowLoginPrompt(false)
          navigate('/') // Redirect to home if user closes without logging in
        }}
        action="view your cart"
      />
    </div>
  )
}

export default Cart
