import React, { useState, useEffect } from 'react'
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom'
import { FaShare, FaShoppingCart, FaPlus, FaMinus, FaCheck, FaTimes, FaChevronLeft, FaChevronRight, FaBolt } from 'react-icons/fa'
import { toast } from 'sonner'
import Button from '../../shared/ui/Button'
import useCartStore from '../../store/cartStore'
import dummyProducts from '../../data/dummyProducts'
import ProductCard from '../components/ProductCard'
import { extractIdFromSlug, getRedirectUrl, parseSlug } from '../../utils/urlUtils'

const ProductDetail = () => {
  const { slug } = useParams()
  const navigate = useNavigate()
  const addToCart = useCartStore(state => state.addToCart)

  // Add custom styles for scrollbar hiding
  React.useEffect(() => {
    const style = document.createElement('style')
    style.textContent = `
      .scrollbar-hide {
        -ms-overflow-style: none;
        scrollbar-width: none;
      }
      .scrollbar-hide::-webkit-scrollbar {
        display: none;
      }
    `
    document.head.appendChild(style)
    return () => document.head.removeChild(style)
  }, [])

  // Parse slug to get product ID
  const slugInfo = parseSlug(slug)
  const productId = slugInfo.id

  // Find product by ID
  const product = dummyProducts.find(p => p.id === productId)

  // State management
  const [selectedImage, setSelectedImage] = useState(0)
  const [quantity, setQuantity] = useState(1)

  // Mock additional images (in real app, this would come from product data)
  const productImages = product ? [
    product.image,
    product.image, // Duplicate for demo - replace with actual additional images
    product.image,
    product.image
  ] : []

  // Related products (same category, excluding current product)
  const relatedProducts = dummyProducts
    .filter(p => p.category === product?.category && p.id !== product?.id)
    .slice(0, 4)

  useEffect(() => {
    if (!product) {
      navigate('/products')
      return
    }

    // Check if we need to redirect to the correct SEO-friendly URL
    const redirectUrl = getRedirectUrl(slug, product)
    if (redirectUrl) {
      navigate(redirectUrl, { replace: true })
      return
    }
  }, [product, navigate, slug])

  // Keyboard navigation for images
  useEffect(() => {
    const handleKeyPress = (e) => {
      if (e.key === 'ArrowLeft') {
        setSelectedImage(prev => prev > 0 ? prev - 1 : productImages.length - 1)
      } else if (e.key === 'ArrowRight') {
        setSelectedImage(prev => prev < productImages.length - 1 ? prev + 1 : 0)
      }
    }

    window.addEventListener('keydown', handleKeyPress)
    return () => window.removeEventListener('keydown', handleKeyPress)
  }, [productImages.length])

  if (!product) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Product Not Found</h2>
          <Button onClick={() => navigate('/products')} variant="primary">
            Back to Products
          </Button>
        </div>
      </div>
    )
  }

  const handleAddToCart = () => {
    addToCart(product, quantity)
    toast.success(`Added ${quantity} ${product.name} to cart`)
  }

  const handleBuyNow = () => {
    // Add to cart first
    addToCart(product, quantity)
    // Navigate directly to checkout
    navigate('/checkout')
    toast.success(`Proceeding to checkout with ${quantity} ${product.name}`)
  }

  const handleQuantityChange = (change) => {
    const newQuantity = quantity + change
    if (newQuantity >= 1 && newQuantity <= 10) {
      setQuantity(newQuantity)
    }
  }



  const shareProduct = () => {
    if (navigator.share) {
      navigator.share({
        title: product.name,
        text: product.description,
        url: window.location.href
      })
    } else {
      navigator.clipboard.writeText(window.location.href)
      toast.success('Product link copied to clipboard')
    }
  }

  // Mock stock status
  const stockStatus = 'in-stock' // 'in-stock', 'limited', 'out-of-stock'
  const stockCount = 15

  return (
    <div className="min-h-screen">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

        {/* Breadcrumb Navigation */}
        <nav className="flex items-center space-x-2 text-sm text-gray-600 mb-8">
          <button onClick={() => navigate('/')} className="hover:text-primary">
            Home
          </button>
          <span>/</span>
          <button onClick={() => navigate('/products')} className="hover:text-primary">
            Products
          </button>
          <span>/</span>
          <button onClick={() => navigate(`/products?category=${product.category}`)} className="hover:text-primary">
            {product.category}
          </button>
          <span>/</span>
          <span className="text-gray-900 font-medium">{product.name}</span>
        </nav>

        {/* Main Product Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">

          {/* Product Images */}
          <div className="space-y-6">
            {/* Main Image */}
            <div className="relative bg-white rounded-2xl overflow-hidden shadow-xl border border-gray-100">
              <div className="relative w-full h-96 lg:h-[500px] group">
                <img
                  src={productImages[selectedImage]}
                  alt={`${product.name} - Main view`}
                  className="w-full h-full object-contain transition-all duration-300"
                />

                {/* Share Button */}
                <button
                  onClick={shareProduct}
                  className="absolute top-4 left-4 p-3 bg-white text-gray-600 hover:text-primary hover:bg-primary hover:bg-opacity-10 rounded-full shadow-lg transition-all duration-300 hover:scale-110"
                >
                  <FaShare className="w-5 h-5" />
                </button>
              </div>
            </div>

            {/* Enhanced Thumbnail Carousel */}
            <div className="flex space-x-3 overflow-x-auto pb-2 scrollbar-hide">
              {productImages.map((image, index) => (
                <button
                  key={index}
                  onClick={() => setSelectedImage(index)}
                  onMouseEnter={() => setSelectedImage(index)}
                  className={`flex-shrink-0 w-24 h-24 m-2 rounded-xl overflow-hidden border-2 transition-all duration-300 transform hover:scale-105 hover:shadow-lg ${
                    selectedImage === index
                      ? 'border-primary  scale-105 ring-2 ring-primary ring-opacity-30'
                      : 'border-gray-200 hover:border-primary hover:shadow-md'
                  }`}
                >
                  <img
                    src={image}
                    alt={`${product.name} view ${index + 1}`}
                    className="w-full h-full object-contain bg-white transition-transform duration-300"
                  />
                </button>
              ))}
            </div>

            {/* Thumbnail Navigation Arrows (for mobile) */}
            <div className="flex justify-between items-center mt-2 sm:hidden">
              <button
                onClick={() => setSelectedImage(prev => prev > 0 ? prev - 1 : productImages.length - 1)}
                className="p-2 rounded-full border border-gray-300 hover:border-primary hover:bg-primary hover:text-white transition-colors"
              >
                <FaChevronLeft className="w-4 h-4" />
              </button>

              <span className="text-sm text-gray-600 font-medium">
                {selectedImage + 1} / {productImages.length}
              </span>

              <button
                onClick={() => setSelectedImage(prev => prev < productImages.length - 1 ? prev + 1 : 0)}
                className="p-2 rounded-full border border-gray-300 hover:border-primary hover:bg-primary hover:text-white transition-colors"
              >
                <FaChevronRight className="w-4 h-4" />
              </button>
            </div>
          </div>

          {/* Product Information */}
          <div className="space-y-6">
            {/* Product Title */}
            <div>
              <h1 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-2">
                {product.name}
              </h1>
              <p className="text-gray-600">SKU: {product.id}</p>
            </div>

            {/* Price */}
            <div className="flex items-center space-x-4">
              <span className="text-3xl font-bold text-primary">
                ₹{product.price.toFixed(2)}
              </span>
              {/* Mock original price for discount display */}
              <span className="text-xl text-gray-500 line-through">
                ₹{(product.price * 1.2).toFixed(2)}
              </span>
              <span className="bg-red-100 text-red-800 px-2 py-1 rounded-full text-sm font-medium">
                17% OFF
              </span>
            </div>

            {/* Stock Status */}
            <div className="flex items-center space-x-2">
              {stockStatus === 'in-stock' && (
                <>
                  <FaCheck className="w-4 h-4 text-green-500" />
                  <span className="text-green-600 font-medium">In Stock ({stockCount} available)</span>
                </>
              )}
              {stockStatus === 'limited' && (
                <>
                  <FaTimes className="w-4 h-4 text-orange-500" />
                  <span className="text-orange-600 font-medium">Limited Stock ({stockCount} left)</span>
                </>
              )}
              {stockStatus === 'out-of-stock' && (
                <>
                  <FaTimes className="w-4 h-4 text-red-500" />
                  <span className="text-red-600 font-medium">Out of Stock</span>
                </>
              )}
            </div>

            {/* Product Description */}
            <div className="prose prose-gray max-w-none">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Description</h3>
              <p className="text-gray-700 leading-relaxed">
                {product.description}
              </p>
            </div>



            {/* Quantity and Add to Cart */}
            <div className="space-y-4">
              {/* Quantity Selector */}
              <div>
                <label className="block text-md font-medium text-gray-900 mb-2">Quantity</label>
                <div className="flex items-center space-x-3">
                  <button
                    onClick={() => handleQuantityChange(-1)}
                    disabled={quantity <= 1}
                    className="p-2 border border-gray-800  rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <FaMinus className="w-3 h-3" />
                  </button>
                  <span className="w-12 text-center font-medium text-lg">{quantity}</span>
                  <button
                    onClick={() => handleQuantityChange(1)}
                    disabled={quantity >= 10}
                    className="p-2 border border-gray-800 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <FaPlus className="w-3 h-3" />
                  </button>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="space-y-3">
                {/* Add to Cart Button */}
                <Button
                  onClick={handleAddToCart}
                  disabled={stockStatus === 'out-of-stock'}
                  variant="primary"
                  className="w-full py-4 text-lg font-semibold rounded-xl flex items-center justify-center space-x-2 transition-all duration-300 hover:shadow-lg"
                >
                  <FaShoppingCart className="w-5 h-5" />
                  <span>
                    {stockStatus === 'out-of-stock' ? 'Out of Stock' : 'Add to Cart'}
                  </span>
                </Button>

                {/* Buy Now Button */}
                <Button
                  onClick={handleBuyNow}
                  disabled={stockStatus === 'out-of-stock'}
                  variant="outline"
                  className="w-full py-4 text-lg font-semibold rounded-xl flex items-center justify-center space-x-2 transition-all duration-300 hover:shadow-lg border-2 border-primary text-primary hover:bg-primary hover:text-white"
                >
                  <span>
                    {stockStatus === 'out-of-stock' ? 'Out of Stock' : 'Buy Now'}
                  </span>
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Related Products */}
        {relatedProducts.length > 0 && (
          <div className="mt-16">
            <div className="flex items-center justify-between mb-8">
              <h2 className="text-2xl font-bold text-gray-900">Related Products</h2>
              <Button
                onClick={() => navigate(`/products?category=${product.category}`)}
                variant="outline"
                size="sm"
              >
                View All
              </Button>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {relatedProducts.map((relatedProduct) => (
                <ProductCard key={relatedProduct.id} product={relatedProduct} />
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default ProductDetail
