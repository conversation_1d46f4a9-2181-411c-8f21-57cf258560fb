import React, { useState, useEffect} from "react";
import Input from "../../shared/ui/Input";
import Button from "../../shared/ui/Button";
import Checkbox from "../../shared/ui/Checkbox";
import logo from "../../assets/Logo1.png";
import { Link, useNavigate, useLocation } from "react-router-dom";
import LinkText from "../../shared/ui/LinkText";
import { AiOutlineEye, AiOutlineEyeInvisible } from "react-icons/ai";
import useAuthStore from "../../store/authStore";
import { toast } from 'sonner'
import { AiOutlineLoading3Quarters } from "react-icons/ai";

const LoginPage = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const {
    login,
    isAuthenticated,
    isLoading,
    error,
    getRememberedEmail,
    clearError
  } = useAuthStore()


  const [formData, setFormData] = useState({
    email: "",
    password: "",
    rememberMe: false
  });

  const [errors, setErrors] = useState({});
  const [show, setShow] = useState(false);

  // Initialize form with remembered email or email from registration
  useEffect(() => {
    const rememberedEmail = getRememberedEmail()
    const emailFromRegistration = location.state?.email

    setFormData(prev => ({
      ...prev,
      email: emailFromRegistration || rememberedEmail || '',
      rememberMe: !!rememberedEmail
    }))

    // Note: Removed toast message to prevent duplicate notifications
  }, [getRememberedEmail, location.state])

  useEffect(() => {
    if(isAuthenticated) {
      navigate("/"); // redirect to homepage or dashboard if already logged in
    }
  }, [isAuthenticated, navigate])

  // handle form data 
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name] : value,
    }));

    // Clear error for that specific field if any
    setErrors((prevErrors) => ({
      ...prevErrors,
      [name]: '',
    }));
  };

  function validateForm(formData) {
    const errors = {};
  
    // Email validation
    if (!formData.email.trim()) {
      errors.email = "Email is required";
    } else if (!/^\S+@\S+\.\S+$/.test(formData.email)) {
      errors.email = "Enter a valid email address";
    }
  
    // Password validation (single-error style)
    if (!formData.password.trim()) {
      errors.password = "Password is required";
    } else if (formData.password.length < 6) {
      errors.password = "Password must be at least 6 characters";
    } else if (!/[A-Z]/.test(formData.password)) {
      errors.password = "Include at least one uppercase letter";
    } else if (!/[0-9]/.test(formData.password)) {
      errors.password = "Include at least one number";
    } else if (!/[!@#$%^&*(),.?":{}|<>]/.test(formData.password)) {
      errors.password = "Include at least one special character";
    } else if (/\s/.test(formData.password)) {
      errors.password = "Password cannot contain spaces";
    }
  
    return errors;
  }

  // handle form submit
  const handleSubmit = async(e) => {
    e.preventDefault();
    // Clear any previous errors
    clearError();

    const validationErrors = validateForm(formData);

    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    setErrors({}); // Clear errors

    try {
      // Password parameter removed - will be added when backend is ready
      const result = await login(formData.email, formData.rememberMe)

      if (result.success) {
        toast.success("Login Successful")

        // Redirect to intended page or home
        const redirectTo = location.state?.from?.pathname || '/'
        navigate(redirectTo, { replace: true })
      } else {
        toast.error(result.error || "Login failed. Please try again.")
      }
    } catch (error) {
      toast.error(error.message || "Login failed. Please try again.");
    }

    //clear form fields (but keep email if remember me is checked)
    setFormData(prev => ({
      ...prev,
      password: '', // Always clear password
      email: formData.rememberMe ? formData.email : '',
    }));
  };

  return (
    <div className="flex flex-col md:flex-row w-full md:p-4">
      {/* Left Image */}
      <div className="hidden md:flex flex-[0.6] p-4 justify-center items-start">
        <img
          src="/images/Krishna.jpeg"
          alt="Hero Background"
          className="w-full h-full object-cover rounded-3xl self-start"
        />
      </div>

      {/* Right Form */}
      <div className="w-full md:flex-[0.4]  flex justify-center  px-4 sm:px-8 md:px-10 lg:px-16 py-8">
        <form
            noValidate
            onSubmit={handleSubmit}
            className="w-full max-w-lg p-4 space-y-6"
          >
            <div className="w-full  max-w-lg p-4  space-y-6">
              {/* Logo */}
              <div className="flex justify-center">
                <Link to="/" className="flex items-center space-x-2 cursor-pointer">
                  <img
                    src={logo}
                    alt="ShringarHub"
                    className="w-32 h-32 object-contain rounded-full"
                  />
                </Link>
              </div>

              {/* Welcome Text */}
              <div className="text-center">
                <h2 className="text-2xl font-semibold">Welcome Back</h2>
                <p className="text-gray-500 text-sm mt-1">
                  Log in to continue your divine shopping journey.
                </p>
              </div>

              {/* Email Input */}
              <Input
                label="Email Address"
                placeholder="Email"
                type="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                error={errors.email}
              />

              {/* Password Input */}
              <div className="relative">
                <Input
                  label="Password"
                  placeholder="Password"
                  type={show ? "text" : "password"}
                  name="password"
                  value={formData.password}
                  onChange={handleChange}
                  error={errors.password}
                />
                <button
                  type="button"
                  onClick={() => setShow(!show)}
                  className="absolute right-4 top-[40px]"
                    aria-label={show ? "Hide password" : "Show password"}

                >
                  {show ? <AiOutlineEyeInvisible /> : <AiOutlineEye />}
                </button>
              </div>

              {/* Remember Me + Forgot Password */}
              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center gap-2 mt-2">
                  <input
                    type="checkbox"
                    name="rememberMe"
                    checked={formData.rememberMe}
                    onChange={handleChange}
                    className="w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary focus:ring-2"
                  />
                  <label htmlFor="rememberMe" className="text-sm text-gray-600 cursor-pointer">
                    Remember me
                  </label>
                </div>
                <Link to="/forgot-password" className="text-primary hover:text-red-700 hover:underline font-medium">
                  Forgot Password?
                </Link>
              </div>

              {/* Login Button */}
              <Button
                type="submit"
                disabled = {isLoading}
                className="w-full rounded">
                  {isLoading && (
                    <AiOutlineLoading3Quarters className="animate-spin mr-2 inline-block" />
                  )}
                  {isLoading ? "Logging in..." : "Login"}
              </Button>

              {/* Sign Up */}
              <p className="text-sm text-center text-gray-600">
                Don't have an account?{" "}
                <LinkText to="/register">Sign Up</LinkText>
              </p>
            </div>
        </form>
      </div>
    </div>
  );
};

export default LoginPage;
