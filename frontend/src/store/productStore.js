import { create } from 'zustand'

const useProductStore = create((set, get) => ({
  // Search state
  searchQuery: '',
  searchResults: [],
  isSearching: false,
  
  // Filter state
  selectedCategory: 'All',
  sortBy: 'name',
  priceRange: { min: 0, max: 2000 },
  
  // Pagination state
  currentPage: 1,
  productsPerPage: 12,
  
  // Loading states
  isLoading: false,
  
  // Mobile filter state
  isMobileFilterOpen: false,
  
  // Actions
  setSearchQuery: (query) => {
    set({
      searchQuery: query,
      currentPage: 1, // Reset to first page when searching
      isSearching: query.length > 0
    })
  },
  
  clearSearch: () => {
    set({ 
      searchQuery: '',
      searchResults: [],
      isSearching: false,
      currentPage: 1
    })
  },
  
  setSelectedCategory: (category) => {
    set({ 
      selectedCategory: category,
      currentPage: 1 // Reset to first page when filtering
    })
  },
  
  setSortBy: (sortBy) => {
    set({ 
      sortBy,
      currentPage: 1 // Reset to first page when sorting
    })
  },
  
  setPriceRange: (priceRange) => {
    set({ 
      priceRange,
      currentPage: 1 // Reset to first page when price changes
    })
  },
  
  setCurrentPage: (page) => {
    set({ currentPage: page })
  },
  
  setIsLoading: (isLoading) => {
    set({ isLoading })
  },
  
  toggleMobileFilter: () => {
    set((state) => ({ 
      isMobileFilterOpen: !state.isMobileFilterOpen 
    }))
  },
  
  closeMobileFilter: () => {
    set({ isMobileFilterOpen: false })
  },
  
  clearAllFilters: () => {
    set({
      searchQuery: '',
      selectedCategory: 'All',
      sortBy: 'name',
      priceRange: { min: 0, max: 2000 },
      currentPage: 1,
      isSearching: false,
      searchResults: []
    })
  },
  
  // Filter products based on current state
  getFilteredProducts: (allProducts) => {
    const state = get()
    console.log('Store state for filtering:', state) // Debug log
    let filtered = allProducts

    // Apply search filter
    if (state.searchQuery) {
      console.log('Applying search filter for:', state.searchQuery) // Debug log
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(state.searchQuery.toLowerCase()) ||
        product.description.toLowerCase().includes(state.searchQuery.toLowerCase())
      )
      console.log('After search filter, products count:', filtered.length) // Debug log
    }
    
    // Apply category filter
    if (state.selectedCategory !== 'All') {
      filtered = filtered.filter(product => 
        product.category === state.selectedCategory
      )
    }
    
    // Apply price filter
    filtered = filtered.filter(product => 
      product.price >= state.priceRange.min && 
      product.price <= state.priceRange.max
    )
    
    // Apply sorting
    filtered.sort((a, b) => {
      switch (state.sortBy) {
        case 'name':
          return a.name.toLowerCase().localeCompare(b.name.toLowerCase())
        case 'name-desc':
          return b.name.toLowerCase().localeCompare(a.name.toLowerCase())
        case 'price':
          return a.price - b.price
        case 'price-desc':
          return b.price - a.price
        case 'date':
          return b.id - a.id
        case 'date-desc':
          return a.id - b.id
        default:
          return a.name.toLowerCase().localeCompare(b.name.toLowerCase())
      }
    })
    
    return filtered
  },
  
  // Get paginated products
  getPaginatedProducts: (filteredProducts) => {
    const state = get()
    const startIndex = (state.currentPage - 1) * state.productsPerPage
    const endIndex = startIndex + state.productsPerPage
    return filteredProducts.slice(startIndex, endIndex)
  }
}))

export default useProductStore
